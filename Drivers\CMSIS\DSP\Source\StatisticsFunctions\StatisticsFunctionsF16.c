/* ----------------------------------------------------------------------
 * Project:      CMSIS DSP Library
 * Title:        StatisticsFunctions.c
 * Description:  Combination of all statistics function source files.
 *
 * $Date:        16. March 2020
 * $Revision:    V1.1.0
 *
 * Target Processor: Cortex-M cores
 * -------------------------------------------------------------------- */
/*
 * Copyright (C) 2019-2020 ARM Limited or its affiliates. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the License); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "arm_max_f16.c"
#include "arm_min_f16.c"
#include "arm_mean_f16.c"
#include "arm_power_f16.c"
#include "arm_rms_f16.c"
#include "arm_std_f16.c"
#include "arm_var_f16.c"
#include "arm_entropy_f16.c"
#include "arm_kullback_leibler_f16.c"
#include "arm_logsumexp_dot_prod_f16.c"
#include "arm_logsumexp_f16.c"
#include "arm_max_no_idx_f16.c"
#include "arm_min_no_idx_f16.c"
#include "arm_absmax_f16.c"
#include "arm_absmin_f16.c"
#include "arm_absmax_no_idx_f16.c"
#include "arm_absmin_no_idx_f16.c"
#include "arm_mse_f16.c"

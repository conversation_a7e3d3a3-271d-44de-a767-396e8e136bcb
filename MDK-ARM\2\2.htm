<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [2\2.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image 2\2.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6230001: Last Updated: Thu Jun 12 20:47:53 2025
<BR><P>
<H3>Maximum Stack Usage =        160 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1c]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1c]">ADC_IRQHandler</a><BR>
 <LI><a href="#[4]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">BusFault_Handler</a><BR>
 <LI><a href="#[2]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2]">HardFault_Handler</a><BR>
 <LI><a href="#[3]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">MemManage_Handler</a><BR>
 <LI><a href="#[1]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1]">NMI_Handler</a><BR>
 <LI><a href="#[5]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">UsageFault_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1c]">ADC_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[4]">BusFault_Handler</a> from stm32f4xx_it.o(.text.BusFault_Handler) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[15]">DMA1_Stream0_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[16]">DMA1_Stream1_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[17]">DMA1_Stream2_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[18]">DMA1_Stream3_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[19]">DMA1_Stream4_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[1a]">DMA1_Stream5_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[1b]">DMA1_Stream6_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[30]">DMA1_Stream7_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[34]">DMA2_Stream0_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[35]">DMA2_Stream1_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[36]">DMA2_Stream2_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[37]">DMA2_Stream3_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[38]">DMA2_Stream4_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[3a]">DMA2_Stream5_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[3b]">DMA2_Stream6_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[3c]">DMA2_Stream7_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[7]">DebugMon_Handler</a> from stm32f4xx_it.o(.text.DebugMon_Handler) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[10]">EXTI0_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[2d]">EXTI15_10_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[11]">EXTI1_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[12]">EXTI2_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[13]">EXTI3_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[14]">EXTI4_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[1d]">EXTI9_5_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[e]">FLASH_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[40]">FPU_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from stm32f4xx_it.o(.text.HardFault_Handler) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[26]">I2C1_ER_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[25]">I2C1_EV_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[28]">I2C2_ER_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[27]">I2C2_EV_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[3f]">I2C3_ER_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[3e]">I2C3_EV_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[3]">MemManage_Handler</a> from stm32f4xx_it.o(.text.MemManage_Handler) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from stm32f4xx_it.o(.text.NMI_Handler) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[39]">OTG_FS_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[2f]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[b]">PVD_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[8]">PendSV_Handler</a> from stm32f4xx_it.o(.text.PendSV_Handler) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[f]">RCC_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[2e]">RTC_Alarm_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[d]">RTC_WKUP_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[31]">SDIO_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[29]">SPI1_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[2a]">SPI2_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[33]">SPI3_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[41]">SPI4_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[6]">SVC_Handler</a> from stm32f4xx_it.o(.text.SVC_Handler) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[9]">SysTick_Handler</a> from stm32f4xx_it.o(.text.SysTick_Handler) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[43]">SystemInit</a> from system_stm32f4xx.o(.text.SystemInit) referenced from startup_stm32f401xx.o(.text)
 <LI><a href="#[c]">TAMP_STAMP_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[1e]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[21]">TIM1_CC_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[20]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[1f]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[22]">TIM2_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[23]">TIM3_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[24]">TIM4_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[32]">TIM5_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[2b]">USART1_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[2c]">USART2_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[3d]">USART6_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[5]">UsageFault_Handler</a> from stm32f4xx_it.o(.text.UsageFault_Handler) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[a]">WWDG_IRQHandler</a> from startup_stm32f401xx.o(.text) referenced from startup_stm32f401xx.o(RESET)
 <LI><a href="#[44]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32f401xx.o(.text)
 <LI><a href="#[42]">main</a> from main.o(.text.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[44]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(.text)
</UL>
<P><STRONG><a name="[5c]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[45]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[4d]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[5d]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[5e]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[5f]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[60]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[61]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f401xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
</UL>

<P><STRONG><a name="[4b]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[62]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[63]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[4a]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[5a]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[64]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[4c]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[49]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[65]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[48]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[66]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[46]"></a>__scatterload</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[67]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[4]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>HAL_GetTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
</UL>

<P><STRONG><a name="[58]"></a>HAL_IncTick</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[4e]"></a>HAL_Init</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, stm32f4xx_hal.o(.text.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[50]"></a>HAL_InitTick</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, stm32f4xx_hal.o(.text.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[51]"></a>HAL_MspInit</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_hal_msp.o(.text.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[53]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 86 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[4f]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[54]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 356 bytes, Stack size 24 bytes, stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[55]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[57]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 940 bytes, Stack size 32 bytes, stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[52]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config))
<BR><BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>MX_GPIO_Init</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, gpio.o(.text.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = MX_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[3]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.NMI_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>SysTick_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.SysTick_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>SystemClock_Config</STRONG> (Thumb, 164 bytes, Stack size 88 bytes, main.o(.text.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[43]"></a>SystemInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, system_stm32f4xx.o(.text.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(.text)
</UL>
<P><STRONG><a name="[5]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f401xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>main</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, main.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = main &rArr; SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[68]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[69]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[6a]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)
<P>
<H3>
Local Symbols
</H3><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>

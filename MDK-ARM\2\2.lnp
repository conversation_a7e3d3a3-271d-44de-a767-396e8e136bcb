--cpu=Cortex-M4 --fpu=SoftVFP
"2\main.o"
"2\gpio.o"
"2\stm32f4xx_it.o"
"2\stm32f4xx_hal_msp.o"
"2\stm32f4xx_hal_rcc.o"
"2\stm32f4xx_hal_rcc_ex.o"
"2\stm32f4xx_hal_flash.o"
"2\stm32f4xx_hal_flash_ex.o"
"2\stm32f4xx_hal_flash_ramfunc.o"
"2\stm32f4xx_hal_gpio.o"
"2\stm32f4xx_hal_dma_ex.o"
"2\stm32f4xx_hal_dma.o"
"2\stm32f4xx_hal_pwr.o"
"2\stm32f4xx_hal_pwr_ex.o"
"2\stm32f4xx_hal_cortex.o"
"2\stm32f4xx_hal.o"
"2\stm32f4xx_hal_exti.o"
"2\system_stm32f4xx.o"
"2\startup_stm32f401xx.o"
--library_type=microlib --strict --scatter "2\2.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "./2/\2.map" -o 2\2.axf